import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Plus, Search, Eye, Users, X } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useNavigate } from 'react-router-dom';
import { toast } from '@/hooks/use-toast';

interface ClientUnit {
  client_unit_hash: string;
  primary_client_name: string;
  secondary_client_name: string | null;
  primary_client_mobile: string;
  primary_client_email: string | null;
  unit_type: string;
  total_investments: number;
  total_investment_amount: string;
  active_investments: number;
  active_amount: string;
}

const Family: React.FC = () => {
  const navigate = useNavigate();
  const [clientUnits, setClientUnits] = useState<ClientUnit[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [searchInput, setSearchInput] = useState('');
  const [filteredUnits, setFilteredUnits] = useState<ClientUnit[]>([]);

  useEffect(() => {
    fetchClientUnits();
  }, []);

  useEffect(() => {
    if (clientUnits.length > 0) {
      filterClientUnits();
    }
  }, [searchTerm, clientUnits]);

  const fetchClientUnits = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase.rpc('get_client_units_summary');
      
      if (error) throw error;
      setClientUnits(data || []);
      setFilteredUnits(data || []);
    } catch (error) {
      console.error('Error fetching client units:', error);
      toast({
        title: "Error",
        description: "Failed to fetch family data",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const filterClientUnits = () => {
    if (!searchTerm) {
      setFilteredUnits(clientUnits);
      return;
    }

    const searchLower = searchTerm.toLowerCase().trim();
    const filtered = clientUnits.filter(unit => 
      (unit.primary_client_name && unit.primary_client_name.toLowerCase().includes(searchLower)) ||
      (unit.secondary_client_name && unit.secondary_client_name.toLowerCase().includes(searchLower)) ||
      (unit.primary_client_mobile && unit.primary_client_mobile.toLowerCase().includes(searchLower)) ||
      (unit.primary_client_email && unit.primary_client_email.toLowerCase().includes(searchLower))
    );
    
    setFilteredUnits(filtered);
  };

  const handleSearch = () => {
    setSearchTerm(searchInput);
  };

  const formatCurrency = (amount: string | number) => {
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
    return '₹' + numAmount.toLocaleString('en-IN', { maximumFractionDigits: 2 });
  };

  if (loading) {
    return <div className="flex items-center justify-center h-64">Loading family data...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Family</h1>
        <div className="flex gap-2">
          <Button onClick={() => navigate('/clients/new')}>
            <Plus className="h-4 w-4 mr-2" />
            Add Client
          </Button>
        </div>
      </div>

      {/* Stats Card */}
      <Card>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex flex-col">
              <span className="text-sm text-gray-500">Total Families</span>
              <span className="text-2xl font-bold">{clientUnits.length}</span>
            </div>
            <div className="flex flex-col">
              <span className="text-sm text-gray-500">Joint Families</span>
              <span className="text-2xl font-bold">
                {clientUnits.filter(unit => unit.unit_type === 'Joint').length}
              </span>
            </div>
            <div className="flex flex-col">
              <span className="text-sm text-gray-500">Individual Families</span>
              <span className="text-2xl font-bold">
                {clientUnits.filter(unit => unit.unit_type === 'Individual').length}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Search */}
      <div className="flex flex-col gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
          <div className="relative w-full">
            <Input
              placeholder="Search by name, email, or mobile..."
              value={searchInput}
              onChange={(e) => {
                setSearchInput(e.target.value);
                if (e.target.value === '') {
                  setSearchTerm('');
                }
              }}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  handleSearch();
                }
              }}
              className="pl-10 pr-20"
              autoComplete="off"
            />
            {searchInput && (
              <Button
                variant="ghost"
                size="sm"
                className="absolute right-16 top-1/2 -translate-y-1/2 h-8 w-8 p-0"
                onClick={() => {
                  setSearchInput('');
                  setSearchTerm('');
                }}
                title="Clear search"
              >
                <X className="h-4 w-4" />
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-1 top-1/2 -translate-y-1/2 h-8 px-3"
              onClick={handleSearch}
            >
              Search
            </Button>
          </div>
        </div>
      </div>

      {/* Family List */}
      <Card>
        <CardHeader>
          <CardTitle>Family Units ({filteredUnits.length} total)</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          {filteredUnits.length > 0 ? (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow className="bg-gray-50">
                    <TableHead className="w-16 text-center font-semibold">S.No</TableHead>
                    <TableHead className="min-w-[200px] font-semibold">Family</TableHead>
                    <TableHead className="min-w-[100px] font-semibold">Type</TableHead>
                    <TableHead className="min-w-[140px] font-semibold">Contact</TableHead>
                    <TableHead className="min-w-[100px] text-center font-semibold">Investments</TableHead>
                    <TableHead className="min-w-[120px] text-right font-semibold">Total Amount</TableHead>
                    <TableHead className="min-w-[120px] text-right sticky right-0 bg-gray-50 font-semibold">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredUnits.map((unit, index) => (
                    <TableRow key={unit.client_unit_hash} className="hover:bg-gray-50">
                      <TableCell className="w-16 text-center font-medium">
                        <span className="text-sm font-bold text-blue-600">{index + 1}</span>
                      </TableCell>
                      <TableCell className="min-w-[200px]">
                        <div className="font-medium text-gray-900">{unit.primary_client_name}</div>
                        {unit.secondary_client_name && (
                          <div className="text-sm text-gray-500 mt-1 font-medium">& {unit.secondary_client_name}</div>
                        )}
                      </TableCell>
                      <TableCell className="min-w-[100px]">
                        <Badge variant={unit.unit_type === 'Joint' ? 'outline' : 'secondary'}>
                          {unit.unit_type}
                        </Badge>
                      </TableCell>
                      <TableCell className="min-w-[140px]">
                        <div className="text-sm font-medium">{unit.primary_client_mobile}</div>
                        <div className="text-sm text-gray-500 truncate">{unit.primary_client_email || 'N/A'}</div>
                      </TableCell>
                      <TableCell className="min-w-[100px] text-center">
                        <div className="font-medium">{unit.total_investments}</div>
                        <div className="text-xs text-gray-500">{unit.active_investments} active</div>
                      </TableCell>
                      <TableCell className="min-w-[120px] text-right">
                        <div className="font-medium text-green-600">{formatCurrency(unit.total_investment_amount)}</div>
                        <div className="text-xs text-gray-500">{formatCurrency(unit.active_amount)} active</div>
                      </TableCell>
                      <TableCell className="min-w-[120px] text-right sticky right-0 bg-white">
                        <div className="flex justify-end gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => navigate(`/family/${unit.client_unit_hash}`)}
                            className="h-8 w-8 p-0 hover:bg-blue-50"
                            title="View Family Details"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <div className="text-center py-12">
              <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No family units found</h3>
              <p className="text-gray-500 mb-4">
                {searchTerm 
                  ? 'Try adjusting your search'
                  : 'Get started by adding clients and creating investments'
                }
              </p>
              {!searchTerm && (
                <Button onClick={() => navigate('/clients/new')}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Client
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default Family;