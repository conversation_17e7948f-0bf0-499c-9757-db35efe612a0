-- Fix the get_client_units_summary function to ensure correct return types
CREATE OR REPLACE FUNCTION public.get_client_units_summary()
RETURNS TABLE (
  client_unit_hash VARCHAR(64),
  primary_client_name TEXT,
  secondary_client_name TEXT,
  primary_client_mobile TEXT, -- Changed from VARCHAR(20) to TEXT
  primary_client_email VARCHAR(255), -- Changed from TEXT to VARCHAR(255)
  unit_type TEXT,
  total_investments BIGINT,
  total_investment_amount DECIMAL,
  active_investments BIGINT,
  active_amount DECIMAL
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    i.client_unit_hash,
    CONCAT(pc.first_name, ' ', pc.last_name) as primary_client_name,
    CASE 
      WHEN sc.id IS NOT NULL THEN CONCAT(sc.first_name, ' ', sc.last_name)
      ELSE NULL
    END as secondary_client_name,
    pc.mobile_number::TEXT as primary_client_mobile, -- Cast to TEXT
    pc.email as primary_client_email,
    CASE 
      WHEN sc.id IS NOT NULL THEN 'Joint'
      ELSE 'Individual'
    END as unit_type,
    COUNT(i.id) as total_investments,
    COALESCE(SUM(i.amount), 0) as total_investment_amount,
    COUNT(i.id) FILTER (WHERE i.status = 'active') as active_investments,
    COALESCE(SUM(i.amount) FILTER (WHERE i.status = 'active'), 0) as active_amount
  FROM public.investments i
  JOIN public.clients pc ON i.client_id = pc.id
  LEFT JOIN public.clients sc ON i.second_applicant_id = sc.id
  WHERE pc.is_deleted = false
  GROUP BY i.client_unit_hash, pc.first_name, pc.last_name, pc.mobile_number, pc.email, sc.first_name, sc.last_name, sc.id
  ORDER BY total_investment_amount DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION public.get_client_units_summary() TO authenticated;