-- Migration: 20250716000004_group_id.sql
-- Adds client unit management for treating joint investments as single client units

-- Add client_unit_hash to investments table
ALTER TABLE public.investments 
ADD COLUMN client_unit_hash VARCHAR(64);

-- Create index for better performance
CREATE INDEX idx_investments_client_unit_hash ON public.investments(client_unit_hash);

-- Function to generate client unit hash
CREATE OR REPLACE FUNCTION public.generate_client_unit_hash(
  primary_client_id UUID,
  secondary_client_id UUID DEFAULT NULL
)
RETURNS VARCHAR(64) AS $$
BEGIN
  IF secondary_client_id IS NULL THEN
    RETURN MD5(primary_client_id::text);
  ELSE
    -- Always put smaller UUID first for consistent hashing
    RETURN MD5(
      LEAST(primary_client_id::text, secondary_client_id::text) || '|' ||
      GREATEST(primary_client_id::text, secondary_client_id::text)
    );
  END IF;
END;
$$ LANGUAGE plpgsql;

-- Function to automatically set client_unit_hash for investments
CREATE OR REPLACE FUNCTION public.handle_investment_client_unit_hash()
<PERSON><PERSON><PERSON>NS TRIGGER AS $$
BEGIN
  NEW.client_unit_hash := public.generate_client_unit_hash(NEW.client_id, NEW.second_applicant_id);
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for investment client unit hash
CREATE TRIGGER trigger_handle_investment_client_unit_hash
  BEFORE INSERT OR UPDATE ON public.investments
  FOR EACH ROW
  EXECUTE FUNCTION public.handle_investment_client_unit_hash();

-- Update existing investments with client_unit_hash
UPDATE public.investments 
SET client_unit_hash = public.generate_client_unit_hash(client_id, second_applicant_id)
WHERE client_unit_hash IS NULL;

-- Function to get client units summary
CREATE OR REPLACE FUNCTION public.get_client_units_summary()
RETURNS TABLE (
  client_unit_hash VARCHAR(64),
  primary_client_name TEXT,
  secondary_client_name TEXT,
  primary_client_mobile TEXT,
  primary_client_email TEXT,
  unit_type TEXT,
  total_investments BIGINT,
  total_investment_amount DECIMAL,
  active_investments BIGINT,
  active_amount DECIMAL
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    i.client_unit_hash,
    CONCAT(pc.first_name, ' ', pc.last_name) as primary_client_name,
    CASE 
      WHEN sc.id IS NOT NULL THEN CONCAT(sc.first_name, ' ', sc.last_name)
      ELSE NULL
    END as secondary_client_name,
    pc.mobile_number as primary_client_mobile,
    pc.email as primary_client_email,
    CASE 
      WHEN sc.id IS NOT NULL THEN 'Joint'
      ELSE 'Individual'
    END as unit_type,
    COUNT(i.id) as total_investments,
    COALESCE(SUM(i.amount), 0) as total_investment_amount,
    COUNT(i.id) FILTER (WHERE i.status = 'active') as active_investments,
    COALESCE(SUM(i.amount) FILTER (WHERE i.status = 'active'), 0) as active_amount
  FROM public.investments i
  JOIN public.clients pc ON i.client_id = pc.id
  LEFT JOIN public.clients sc ON i.second_applicant_id = sc.id
  WHERE pc.is_deleted = false
  GROUP BY i.client_unit_hash, pc.first_name, pc.last_name, pc.mobile_number, pc.email, sc.first_name, sc.last_name, sc.id
  ORDER BY total_investment_amount DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get investments by client unit
CREATE OR REPLACE FUNCTION public.get_investments_by_client_unit(unit_hash VARCHAR(64))
RETURNS TABLE (
  investment_id UUID,
  scheme_name TEXT,
  amount DECIMAL,
  investment_date DATE,
  maturity_date DATE,
  status TEXT,
  primary_client TEXT,
  secondary_client TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    i.id as investment_id,
    i.scheme_name,
    i.amount,
    i.investment_date,
    i.maturity_date,
    i.status,
    CONCAT(pc.first_name, ' ', pc.last_name) as primary_client,
    CASE 
      WHEN sc.id IS NOT NULL THEN CONCAT(sc.first_name, ' ', sc.last_name)
      ELSE NULL
    END as secondary_client
  FROM public.investments i
  JOIN public.clients pc ON i.client_id = pc.id
  LEFT JOIN public.clients sc ON i.second_applicant_id = sc.id
  WHERE i.client_unit_hash = unit_hash
  ORDER BY i.investment_date DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to count total client units
CREATE OR REPLACE FUNCTION public.get_total_client_units()
RETURNS BIGINT AS $$
BEGIN
  RETURN (
    SELECT COUNT(DISTINCT client_unit_hash) 
    FROM public.investments i
    JOIN public.clients c ON i.client_id = c.id
    WHERE c.is_deleted = false
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION public.generate_client_unit_hash(UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_client_units_summary() TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_investments_by_client_unit(VARCHAR(64)) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_total_client_units() TO authenticated;

-- Add comments for documentation
COMMENT ON COLUMN public.investments.client_unit_hash IS 'Unique hash identifying client combination for this investment';
COMMENT ON FUNCTION public.generate_client_unit_hash(UUID, UUID) IS 'Generates unique hash for client combination';
COMMENT ON FUNCTION public.get_client_units_summary() IS 'Returns summary of all client units (each unique client combination)';
COMMENT ON FUNCTION public.get_investments_by_client_unit(VARCHAR(64)) IS 'Returns all investments for a specific client unit';
COMMENT ON FUNCTION public.get_total_client_units() IS 'Returns total count of unique client units';
