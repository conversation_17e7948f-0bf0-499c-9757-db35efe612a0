-- Fix the get_investments_by_client_unit function to ensure correct return types
CREATE OR REPLACE FUNCTION public.get_investments_by_client_unit(unit_hash VARCHAR(64))
RETURNS TABLE (
  investment_id UUID,
  scheme_name TEXT,
  amount DECIMAL,
  investment_date DATE,
  maturity_date DATE,
  maturity_amount DECIMAL,
  status TEXT,
  primary_client TEXT,
  secondary_client TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    i.id as investment_id,
    i.scheme_name::TEXT,
    i.amount,
    i.investment_date,
    i.maturity_date,
    i.maturity_amount,
    i.status::TEXT,
    CONCAT(pc.first_name, ' ', pc.last_name)::TEXT as primary_client,
    CASE 
      WHEN sc.id IS NOT NULL THEN CONCAT(sc.first_name, ' ', sc.last_name)::TEXT
      ELSE NULL::TEXT
    END as secondary_client
  FROM public.investments i
  JOIN public.clients pc ON i.client_id = pc.id
  LEFT JOIN public.clients sc ON i.second_applicant_id = sc.id
  WHERE i.client_unit_hash = unit_hash
  ORDER BY i.investment_date DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION public.get_investments_by_client_unit(VARCHAR(64)) TO authenticated;